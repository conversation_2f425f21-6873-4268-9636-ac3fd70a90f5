package com.telecom.nrm.dao;

import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.annotation.DaAPI;
import com.telecom.da.client.annotation.DaClientProxy;
import com.telecom.da.client.annotation.DaParam;
import com.telecom.da.client.annotation.DaShardingCode;
import com.telecom.da.client.domain.PageResponse;

import java.util.List;

/**
 * 分光器端口预警数据访问接口
 * 对应前端 splitter-port.vue 页面的所有数据查询需求
 * 
 * 主要功能：
 * 1. 设备容器数据查询
 * 2. 分光器详情数据查询
 * 3. 历史趋势数据查询
 * 4. 统计数据查询
 * 5. 预测结果数据更新
 * 6. 区域数据查询
 * 7. 预警处理数据更新
 * 
 * 注意：所有API都需要根据实际数据库结构进行调整
 * 🔌 后端对接：需要创建对应的数据库API接口
 */
@DaClientProxy
public interface SplitterDao {

    /**
     * 查询设备列表（分页）
     * 🎭 替换前端 deviceList 演示数据
     * 支持按设备编码、区域、设备状态等条件筛选
     * 
     * @param param 查询参数JSON对象
     * @param pageSize 每页大小
     * @param currentPage 当前页码
     * @param shardingCode 分片代码
     * @return 分页的设备列表数据
     */
    @DaAPI(apiCode = "splitter_device_list_query", version = "*********000001", returnTypes = {PageResponse.class, JSONObject.class})
    PageResponse<JSONObject> queryDeviceList(@DaParam JSONObject param, 
                                           @DaParam(name = "pageSize") Integer pageSize,
                                           @DaParam(name = "currentPage") Integer currentPage, 
                                           @DaShardingCode String shardingCode);

    /**
     * 查询设备统计数据
     * 🎭 替换前端 statistics 演示数据
     * 统计正常、注意、告警设备数量和最后更新时间
     * 
     * @param param 查询参数
     * @param shardingCode 分片代码
     * @return 统计数据JSON对象
     */
    @DaAPI(apiCode = "splitter_statistics_query", version = "*********000002", returnTypes = {JSONObject.class})
    JSONObject queryStatistics(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 查询设备分光器详情
     * 🎭 替换前端 splitterDetails 演示数据
     * 根据设备编码查询该设备下所有分光器的详细信息
     * 
     * @param param 查询参数，包含设备编码
     * @param shardingCode 分片代码
     * @return 分光器详情列表
     */
    @DaAPI(apiCode = "splitter_detail_query", version = "*********000003", returnTypes = {List.class})
    List<JSONObject> querySplitterDetails(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 查询设备历史数据
     * 🎭 替换前端历史数据，用于预测分析
     * 查询设备的历史使用率数据，用于趋势分析和预测
     * 
     * @param param 查询参数，包含设备编码和时间范围
     * @param shardingCode 分片代码
     * @return 历史数据列表
     */
    @DaAPI(apiCode = "splitter_history_query", version = "*********000004", returnTypes = {List.class})
    List<JSONObject> queryHistoricalData(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 查询趋势数据
     * 🎭 替换前端 trendData 演示数据
     * 根据设备编码和时间范围查询趋势图数据
     * 
     * @param param 查询参数，包含设备编码和时间范围
     * @param shardingCode 分片代码
     * @return 趋势数据列表
     */
    @DaAPI(apiCode = "splitter_trend_query", version = "*********000005", returnTypes = {List.class})
    List<JSONObject> queryTrendData(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 查询所有设备基础信息
     * 🎭 用于批量预测功能
     * 查询所有设备的基础信息，用于批量预测分析
     * 
     * @param param 查询参数
     * @param shardingCode 分片代码
     * @return 设备基础信息列表
     */
    @DaAPI(apiCode = "splitter_all_devices_query", version = "*********000006", returnTypes = {List.class})
    List<JSONObject> queryAllDevices(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 更新设备预测结果
     * 🔌 后端对接：将预测分析结果保存到数据库
     * 更新设备的预测实占率、预测空闲数、预测状态等信息
     * 
     * @param param 更新参数，包含设备编码和预测结果
     * @param shardingCode 分片代码
     * @return 更新记录数
     */
    @DaAPI(apiCode = "splitter_prediction_update", version = "*********000007", returnTypes = {Integer.class})
    Integer updatePredictionResult(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 批量更新预测结果
     * 🔌 后端对接：批量更新多个设备的预测结果
     * 
     * @param param 批量更新参数
     * @param shardingCode 分片代码
     * @return 更新记录数
     */
    @DaAPI(apiCode = "splitter_batch_prediction_update", version = "*********000008", returnTypes = {Integer.class})
    Integer batchUpdatePredictionResult(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 查询区域列表
     * 🎭 替换前端区域下拉选项演示数据
     * 查询所有可用的区域信息
     * 
     * @param param 查询参数
     * @param shardingCode 分片代码
     * @return 区域列表
     */
    @DaAPI(apiCode = "splitter_regions_query", version = "*********000009", returnTypes = {List.class})
    List<JSONObject> queryRegions(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 更新设备预警处理状态
     * 🎭 替换前端预警处理功能
     * 更新设备的预警处理状态和处理信息
     * 
     * @param param 更新参数，包含设备编码和处理信息
     * @param shardingCode 分片代码
     * @return 更新记录数
     */
    @DaAPI(apiCode = "splitter_warning_handle_update", version = "*********000010", returnTypes = {Integer.class})
    Integer updateWarningStatus(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 插入历史数据记录
     * 🔌 后端对接：定时任务插入设备使用率历史数据
     * 用于趋势分析和预测算法的数据基础
     * 
     * @param param 插入参数
     * @param shardingCode 分片代码
     * @return 插入记录数
     */
    @DaAPI(apiCode = "splitter_history_insert", version = "*********000011", returnTypes = {Integer.class})
    Integer insertHistoryData(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 查询设备详细信息
     * 🔌 后端对接：根据设备编码查询单个设备的详细信息
     * 
     * @param param 查询参数，包含设备编码
     * @param shardingCode 分片代码
     * @return 设备详细信息
     */
    @DaAPI(apiCode = "splitter_device_detail_query", version = "*********000012", returnTypes = {JSONObject.class})
    JSONObject queryDeviceDetail(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 查询预测配置参数
     * 🔌 后端对接：查询预测算法的配置参数
     * 如阈值设置、算法参数等
     * 
     * @param param 查询参数
     * @param shardingCode 分片代码
     * @return 配置参数JSON对象
     */
    @DaAPI(apiCode = "splitter_prediction_config_query", version = "*********000013", returnTypes = {JSONObject.class})
    JSONObject queryPredictionConfig(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 更新预测配置参数
     * 🔌 后端对接：更新预测算法的配置参数
     * 
     * @param param 更新参数
     * @param shardingCode 分片代码
     * @return 更新记录数
     */
    @DaAPI(apiCode = "splitter_prediction_config_update", version = "*********000014", returnTypes = {Integer.class})
    Integer updatePredictionConfig(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 查询导出数据
     * 🎭 替换前端导出功能
     * 根据筛选条件查询需要导出的设备数据
     * 
     * @param param 查询参数，包含筛选条件
     * @param shardingCode 分片代码
     * @return 导出数据列表
     */
    @DaAPI(apiCode = "splitter_export_data_query", version = "*********000015", returnTypes = {List.class})
    List<JSONObject> queryExportData(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 记录操作日志
     * 🔌 后端对接：记录用户操作日志
     * 记录预测、导出、预警处理等操作的日志信息
     * 
     * @param param 日志参数
     * @param shardingCode 分片代码
     * @return 插入记录数
     */
    @DaAPI(apiCode = "splitter_operation_log_insert", version = "*********000016", returnTypes = {Integer.class})
    Integer insertOperationLog(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 查询预警规则配置
     * 🔌 后端对接：查询预警规则配置
     * 查询实占率阈值、预警级别等配置信息
     * 
     * @param param 查询参数
     * @param shardingCode 分片代码
     * @return 预警规则配置
     */
    @DaAPI(apiCode = "splitter_warning_rules_query", version = "*********000017", returnTypes = {List.class})
    List<JSONObject> queryWarningRules(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 更新预警规则配置
     * 🔌 后端对接：更新预警规则配置
     *
     * @param param 更新参数
     * @param shardingCode 分片代码
     * @return 更新记录数
     */
    @DaAPI(apiCode = "splitter_warning_rules_update", version = "*********000018", returnTypes = {Integer.class})
    Integer updateWarningRules(@DaParam JSONObject param, @DaShardingCode String shardingCode);

    /**
     * 调用 obd_port_alert API 获取设备数据
     * 🔌 新增：对接 obd_port_alert 数据源
     * 支持分页查询和筛选条件
     *
     * @param param 查询参数JSON对象
     * @param pageSize 每页大小
     * @param currentPage 当前页码
     * @param shardingCode 分片代码
     * @return 分页的设备列表数据
     */
    @DaAPI(apiCode = "obd_port_alert", version = "V20250731165928869", returnTypes = {PageResponse.class, JSONObject.class})
    PageResponse<JSONObject> queryObdPortAlertData(@DaParam JSONObject param,
                                                 @DaParam(name = "pageSize") Integer pageSize,
                                                 @DaParam(name = "currentPage") Integer currentPage,
                                                 @DaShardingCode String shardingCode);
}
