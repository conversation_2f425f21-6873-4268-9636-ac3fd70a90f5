"""
测试真实API数据集成
用于验证字段映射和数据转换功能
"""

import requests
import json
from datetime import datetime

# 真实API数据示例（基于你提供的数据）
REAL_API_SAMPLE_DATA = [
    {
        "rooms": 247,
        "code": "250JN.DSQ00/GJ005/ZHX007",
        "utilization_rate": "91.67%",
        "address_desc": "南京市江宁区谷里街道",
        "leaf_region_id": "321122930000000000000005",
        "leaf_region": "江宁",
        "dt": 1753891200000,
        "obd": 3,
        "ftth": 88,
        "free_capacity": "8",
        "spec_id": 1020300002,
        "name": "东善桥ODF004",
        "all_capacity": "96",
        "id": "321250000000000360863343",
        "create_date": "2023-10-13T00:53:29.000+08:00"
    },
    {
        "rooms": 320,
        "code": "250JN.DSQ00/GJ005/ZHX008",
        "utilization_rate": "75.50%",
        "address_desc": "南京市江宁区谷里街道",
        "leaf_region_id": "321122930000000000000005",
        "leaf_region": "江宁",
        "dt": 1753977600000,  # 下一周的时间戳
        "obd": 4,
        "ftth": 96,
        "free_capacity": "31",
        "spec_id": 1020300002,
        "name": "东善桥ODF005",
        "all_capacity": "128",
        "id": "321250000000000360863344",
        "create_date": "2023-10-13T00:53:29.000+08:00"
    }
]

def test_data_conversion():
    """测试数据格式转换接口"""
    print("=== 测试数据格式转换 ===")
    
    url = "http://localhost:8000/api/convert/real-to-system"
    
    try:
        response = requests.post(url, json=REAL_API_SAMPLE_DATA)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 数据转换成功")
            print(f"原始数据条数: {result['original_count']}")
            print(f"转换后数据条数: {result['converted_count']}")
            print(f"转换消息: {result['message']}")
            
            # 打印转换后的数据示例
            if result['converted_data']:
                print("\n转换后的数据示例:")
                first_converted = result['converted_data'][0]
                for key, value in first_converted.items():
                    print(f"  {key}: {value}")
                    
            return True
        else:
            print(f"❌ 数据转换失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def test_single_device_prediction():
    """测试单设备预测（使用真实API数据）"""
    print("\n=== 测试单设备预测（真实API数据） ===")
    
    url = "http://localhost:8000/api/predict/real-data/single"
    
    request_data = {
        "deviceCode": "250JN.DSQ00/GJ005/ZHX007",
        "realApiData": REAL_API_SAMPLE_DATA
    }
    
    try:
        response = requests.post(url, json=request_data)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 单设备预测成功")
            print(f"设备编码: {result['设备编码']}")
            print(f"是否有数据: {result['hasData']}")
            
            if result['predictionResults']:
                pred = result['predictionResults']
                print(f"下周预测实占率: {pred['nextWeekRate']}%")
                print(f"下月预测实占率: {pred['nextMonthRate']}%")
                print(f"趋势: {pred['trend']}")
            
            if result['riskAssessmentAndAdvice']:
                risk = result['riskAssessmentAndAdvice']
                print(f"风险等级: {risk['riskLevel']}")
                print(f"风险原因: {risk['riskReason']}")
                print(f"建议: {risk['advice']}")
                
            return True
        else:
            print(f"❌ 单设备预测失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def test_batch_prediction():
    """测试批量预测（使用真实API数据）"""
    print("\n=== 测试批量预测（真实API数据） ===")
    
    url = "http://localhost:8000/api/predict/real-data/batch"
    
    request_data = {
        "devices": [
            {
                "deviceCode": "250JN.DSQ00/GJ005/ZHX007",
                "realApiData": [REAL_API_SAMPLE_DATA[0]]  # 第一个设备的数据
            },
            {
                "deviceCode": "250JN.DSQ00/GJ005/ZHX008", 
                "realApiData": [REAL_API_SAMPLE_DATA[1]]  # 第二个设备的数据
            }
        ]
    }
    
    try:
        response = requests.post(url, json=request_data)
        
        if response.status_code == 200:
            results = response.json()
            print(f"✅ 批量预测成功，共预测 {len(results)} 个设备")
            
            for i, result in enumerate(results):
                print(f"\n设备 {i+1}:")
                print(f"  设备编码: {result['设备编码']}")
                print(f"  预测实占率: {result['预测实占率']}%")
                print(f"  预测空闲数: {result['预测空闲数']}")
                print(f"  预测状态: {result['预测状态']}")
                print(f"  风险等级: {result['riskLevel']}")
                print(f"  趋势: {result['trend']}")
                
            return True
        else:
            print(f"❌ 批量预测失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def test_health_check():
    """测试健康检查接口"""
    print("=== 测试健康检查 ===")
    
    url = "http://localhost:8000/api/health"
    
    try:
        response = requests.get(url)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 服务健康检查通过")
            print(f"服务状态: {result['status']}")
            print(f"服务名称: {result['service']}")
            print(f"版本: {result['version']}")
            print(f"TensorFlow可用: {result['tensorflow_available']}")
            return True
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 健康检查请求失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试真实API数据集成...")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)
    
    # 测试序列
    tests = [
        ("健康检查", test_health_check),
        ("数据格式转换", test_data_conversion),
        ("单设备预测", test_single_device_prediction),
        ("批量预测", test_batch_prediction)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        success = test_func()
        results.append((test_name, success))
    
    # 汇总结果
    print("\n" + "="*50)
    print("测试结果汇总:")
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    success_count = sum(1 for _, success in results if success)
    total_count = len(results)
    print(f"\n总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count == total_count:
        print("🎉 所有测试都通过了！真实API数据集成成功！")
    else:
        print("⚠️  部分测试失败，请检查服务状态和配置")

if __name__ == "__main__":
    main()
