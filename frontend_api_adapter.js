/**
 * 前端API适配器
 * 用于处理真实数据源API和前端界面的数据交互
 */

// API基础配置
const API_BASE_URL = 'http://localhost:8000/api';

/**
 * 真实数据源API适配器类
 */
class RealDataApiAdapter {
  
  /**
   * 调用真实数据源单设备预测API
   * @param {string} deviceCode - 设备编码
   * @param {Array} realApiData - 真实API数据数组
   * @returns {Promise} 预测结果
   */
  async predictSingleDeviceWithRealData(deviceCode, realApiData) {
    try {
      const response = await fetch(`${API_BASE_URL}/predict/real-data/single`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          deviceCode: deviceCode,
          realApiData: realApiData
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return this.adaptPredictionResultForFrontend(result);
    } catch (error) {
      console.error('单设备预测失败:', error);
      throw error;
    }
  }

  /**
   * 调用真实数据源批量预测API
   * @param {Array} devices - 设备数组，每个设备包含deviceCode和realApiData
   * @returns {Promise} 批量预测结果
   */
  async predictBatchDevicesWithRealData(devices) {
    try {
      const response = await fetch(`${API_BASE_URL}/predict/real-data/batch`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          devices: devices
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const results = await response.json();
      return results.map(result => this.adaptBatchResultForFrontend(result));
    } catch (error) {
      console.error('批量预测失败:', error);
      throw error;
    }
  }

  /**
   * 数据格式转换（调试用）
   * @param {Array} realApiData - 真实API数据
   * @returns {Promise} 转换结果
   */
  async convertRealDataToSystemFormat(realApiData) {
    try {
      const response = await fetch(`${API_BASE_URL}/convert/real-to-system`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(realApiData)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('数据转换失败:', error);
      throw error;
    }
  }

  /**
   * 将预测结果适配为前端期望的格式
   * @param {Object} apiResult - API返回的预测结果
   * @returns {Object} 适配后的结果
   */
  adaptPredictionResultForFrontend(apiResult) {
    return {
      hasData: apiResult.hasData,
      设备编码: apiResult.设备编码,
      historicalData: apiResult.historicalData || [],
      
      // 预测结果模块
      predictionResults: apiResult.predictionResults ? {
        nextWeekRate: apiResult.predictionResults.nextWeekRate,
        nextMonthRate: apiResult.predictionResults.nextMonthRate,
        trend: apiResult.predictionResults.trend
      } : null,
      
      // 风险评估与建议模块
      riskAssessmentAndAdvice: apiResult.riskAssessmentAndAdvice ? {
        riskLevel: apiResult.riskAssessmentAndAdvice.riskLevel,
        riskReason: apiResult.riskAssessmentAndAdvice.riskReason,
        advice: apiResult.riskAssessmentAndAdvice.advice
      } : null
    };
  }

  /**
   * 将批量预测结果适配为前端期望的格式
   * @param {Object} batchResult - 批量预测结果项
   * @returns {Object} 适配后的结果
   */
  adaptBatchResultForFrontend(batchResult) {
    return {
      设备编码: batchResult.设备编码,
      预测实占率: batchResult.预测实占率,
      预测空闲数: batchResult.预测空闲数,
      预测状态: batchResult.预测状态,
      deviceType: batchResult.deviceType,
      riskLevel: batchResult.riskLevel,
      riskScore: batchResult.riskScore,
      trend: batchResult.trend
    };
  }

  /**
   * 将真实API数据转换为前端表格显示格式
   * @param {Array} realApiDataList - 真实API数据列表
   * @returns {Array} 前端表格数据
   */
  convertRealApiDataToTableFormat(realApiDataList) {
    return realApiDataList.map(item => {
      // 计算实占率（数值格式）
      const utilizationRate = parseFloat(item.utilization_rate.replace('%', ''));
      
      // 计算健康状态
      let healthStatus = 'normal';
      if (utilizationRate >= 90) healthStatus = 'alarm';
      else if (utilizationRate >= 80) healthStatus = 'attention';

      return {
        设备编码: item.code,
        区域: item.leaf_region,
        小区入库时间: this.formatDate(item.create_date),
        覆盖的工程级的线路到达房间数: item.rooms,
        分光器数: item.obd,
        分光器容量: parseInt(item.all_capacity),
        分光器空闲数: parseInt(item.free_capacity),
        ftth终端数: item.ftth,
        实占率: utilizationRate,
        健康状态: healthStatus,
        // 原始数据保留
        原始数据: item
      };
    });
  }

  /**
   * 格式化日期
   * @param {string} isoDateString - ISO格式日期字符串
   * @returns {string} 格式化后的日期
   */
  formatDate(isoDateString) {
    try {
      const date = new Date(isoDateString);
      return date.toISOString().split('T')[0]; // YYYY-MM-DD格式
    } catch (error) {
      return isoDateString.substring(0, 10); // 备选方案
    }
  }

  /**
   * 健康检查
   * @returns {Promise} 健康检查结果
   */
  async healthCheck() {
    try {
      const response = await fetch(`${API_BASE_URL}/health`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      console.error('健康检查失败:', error);
      throw error;
    }
  }
}

/**
 * Vue组件中的使用示例
 */
const VueComponentExample = {
  data() {
    return {
      apiAdapter: new RealDataApiAdapter(),
      deviceList: [],
      loading: false,
      // 真实API数据示例
      sampleRealApiData: [
        {
          rooms: 247,
          code: "250JN.DSQ00/GJ005/ZHX007",
          utilization_rate: "91.67%",
          address_desc: "南京市江宁区谷里街道",
          leaf_region_id: "321122930000000000000005",
          leaf_region: "江宁",
          dt: 1753891200000,
          obd: 3,
          ftth: 88,
          free_capacity: "8",
          spec_id: **********,
          name: "东善桥ODF004",
          all_capacity: "96",
          id: "321250000000000360863343",
          create_date: "2023-10-13T00:53:29.000+08:00"
        }
      ]
    };
  },

  methods: {
    /**
     * 加载真实API数据并转换为表格格式
     */
    async loadRealApiData() {
      try {
        this.loading = true;
        
        // 这里应该调用你的真实数据源API
        // const realApiResponse = await fetch('your-real-api-endpoint');
        // const realApiData = await realApiResponse.json();
        
        // 暂时使用示例数据
        const realApiData = this.sampleRealApiData;
        
        // 转换为前端表格格式
        this.deviceList = this.apiAdapter.convertRealApiDataToTableFormat(realApiData);
        
        console.log('设备列表加载成功:', this.deviceList);
      } catch (error) {
        console.error('加载设备列表失败:', error);
        this.$message.error('加载设备列表失败');
      } finally {
        this.loading = false;
      }
    },

    /**
     * 执行单设备预测
     */
    async executeDevicePrediction(deviceCode) {
      try {
        // 找到对应设备的真实API数据
        const device = this.deviceList.find(d => d.设备编码 === deviceCode);
        if (!device || !device.原始数据) {
          throw new Error('设备数据不存在');
        }

        // 调用预测API
        const predictionResult = await this.apiAdapter.predictSingleDeviceWithRealData(
          deviceCode, 
          [device.原始数据]
        );

        console.log('预测结果:', predictionResult);
        return predictionResult;
      } catch (error) {
        console.error('设备预测失败:', error);
        this.$message.error('设备预测失败');
        throw error;
      }
    },

    /**
     * 执行批量预测
     */
    async executeBatchPrediction() {
      try {
        this.loading = true;

        // 准备批量预测数据
        const devices = this.deviceList.map(device => ({
          deviceCode: device.设备编码,
          realApiData: [device.原始数据]
        }));

        // 调用批量预测API
        const batchResults = await this.apiAdapter.predictBatchDevicesWithRealData(devices);

        // 更新设备列表的预测数据
        this.updateDeviceListWithPredictions(batchResults);

        console.log('批量预测完成:', batchResults);
        this.$message.success(`批量预测完成，共预测 ${batchResults.length} 个设备`);
      } catch (error) {
        console.error('批量预测失败:', error);
        this.$message.error('批量预测失败');
      } finally {
        this.loading = false;
      }
    },

    /**
     * 更新设备列表的预测数据
     */
    updateDeviceListWithPredictions(predictions) {
      this.deviceList = this.deviceList.map(device => {
        const prediction = predictions.find(p => p.设备编码 === device.设备编码);
        if (prediction) {
          return {
            ...device,
            预测实占率: prediction.预测实占率,
            预测空闲数: prediction.预测空闲数,
            预测状态: prediction.预测状态
          };
        }
        return device;
      });
    }
  },

  async mounted() {
    // 组件挂载时加载数据
    await this.loadRealApiData();
  }
};

// 导出适配器类
if (typeof module !== 'undefined' && module.exports) {
  module.exports = RealDataApiAdapter;
} else if (typeof window !== 'undefined') {
  window.RealDataApiAdapter = RealDataApiAdapter;
}
