# 真实数据源API集成方案

## 概述

本方案实现了真实数据源API（obd_port_alert）与现有分光器端口预测系统的无缝集成，通过在FastAPI后端添加数据适配层，实现字段映射和格式转换，确保预测逻辑完全不变。

## 字段映射关系

### 核心字段映射

| 真实API字段 | 当前系统字段 | 数据类型转换 | 说明 |
|------------|-------------|-------------|------|
| `code` | `设备编码` | string → string | 设备唯一标识 |
| `leaf_region` | `区域` | string → string | 区域名称 |
| `create_date` | `小区入库时间` | ISO时间 → YYYY-MM-DD | 日期格式转换 |
| `rooms` | `覆盖的工程级的线路到达房间数` | int → int | 覆盖房间数 |
| `ftth` | `ftth终端数` | int → int | FTTH终端数量 |
| `all_capacity` | `分光器容量` | string → int | 字符串转数字 |
| `free_capacity` | `分光器空闲数` | string → int | 字符串转数字 |
| `utilization_rate` | `实占率` | "91.67%" → 91.67 | 百分比字符串转数值 |
| `obd` | `分光器数` | int → int | 分光器数量 |

### 计算字段

| 字段名 | 计算公式 | 说明 |
|-------|---------|------|
| `潜在需求比` | `ftth / rooms` | 基于终端数和房间数计算 |
| `week` | 基于`dt`时间戳生成 | 生成周次标识，如"2024-01" |

### 保留的原始字段

为了扩展性，系统保留了原始API的额外字段：
- `原始_id`: 设备ID
- `原始_spec_id`: 规格ID  
- `原始_leaf_region_id`: 区域ID
- `原始_name`: 设备名称
- `原始_dt`: 原始时间戳

## 技术实现

### 1. 数据适配器 (DataSourceAdapter)

位置：`splitter_prediction_fastapi.py` 第53-181行

核心功能：
- `convert_real_api_to_system_format()`: 单条数据转换
- `convert_batch_real_api_data()`: 批量数据转换
- `parse_utilization_rate()`: 实占率解析
- `convert_iso_date()`: 日期格式转换
- `generate_week_from_timestamp()`: 周次生成

### 2. 新增API接口

#### 单设备预测接口
```
POST /api/predict/real-data/single
```
接收真实API格式数据，转换后调用标准预测逻辑。

#### 批量预测接口
```
POST /api/predict/real-data/batch
```
支持批量处理多个设备的真实API数据。

#### 数据转换调试接口
```
POST /api/convert/real-to-system
```
用于验证数据转换逻辑的正确性。

### 3. 数据模型定义

#### RealApiDataPoint
定义真实API数据结构，包含所有obd_port_alert接口字段。

#### RealApiPredictionRequest
单设备预测请求模型，包含设备编码和真实API数据列表。

#### RealApiBatchPredictionRequest
批量预测请求模型，支持多设备数据。

## 使用方式

### 1. 启动服务

```bash
# 安装依赖
pip install fastapi uvicorn pandas numpy scikit-learn tensorflow statsmodels scipy

# 启动服务
uvicorn splitter_prediction_fastapi:app --host 0.0.0.0 --port 8000
```

### 2. 调用真实数据源API

#### Python示例

```python
import requests

# 真实API数据
real_data = {
    "deviceCode": "250JN.DSQ00/GJ005/ZHX007",
    "realApiData": [
        {
            "rooms": 247,
            "code": "250JN.DSQ00/GJ005/ZHX007",
            "utilization_rate": "91.67%",
            "address_desc": "南京市江宁区谷里街道",
            "leaf_region_id": "321122930000000000000005",
            "leaf_region": "江宁",
            "dt": 1753891200000,
            "obd": 3,
            "ftth": 88,
            "free_capacity": "8",
            "spec_id": 1020300002,
            "name": "东善桥ODF004",
            "all_capacity": "96",
            "id": "321250000000000360863343",
            "create_date": "2023-10-13T00:53:29.000+08:00"
        }
    ]
}

# 调用预测API
response = requests.post(
    "http://localhost:8000/api/predict/real-data/single",
    json=real_data
)

result = response.json()
print(f"预测结果: {result}")
```

#### JavaScript/前端示例

```javascript
// 使用提供的前端适配器
const adapter = new RealDataApiAdapter();

// 单设备预测
const result = await adapter.predictSingleDeviceWithRealData(
    "250JN.DSQ00/GJ005/ZHX007",
    [realApiDataPoint]
);

// 批量预测
const batchResults = await adapter.predictBatchDevicesWithRealData([
    {
        deviceCode: "device1",
        realApiData: [data1]
    },
    {
        deviceCode: "device2", 
        realApiData: [data2]
    }
]);
```

### 3. 前端集成

前端可以通过以下方式集成：

1. **使用新的API接口**：直接调用`/api/predict/real-data/*`接口
2. **数据格式适配**：使用`frontend_api_adapter.js`中的适配器类
3. **界面保持不变**：现有Vue组件无需修改，只需更换数据源

## 测试验证

### 运行集成测试

```bash
# 确保服务已启动
python test_real_api_integration.py
```

测试内容包括：
- 健康检查
- 数据格式转换
- 单设备预测
- 批量预测

### 预期输出

```
=== 测试健康检查 ===
✅ 服务健康检查通过

=== 测试数据格式转换 ===
✅ 数据转换成功
原始数据条数: 2
转换后数据条数: 2

=== 测试单设备预测 ===
✅ 单设备预测成功
下周预测实占率: 93.2%
下月预测实占率: 95.8%

=== 测试批量预测 ===
✅ 批量预测成功，共预测 2 个设备

🎉 所有测试都通过了！真实API数据集成成功！
```

## 优势特点

### 1. 零影响集成
- **预测逻辑完全不变**：所有算法和模型保持原有逻辑
- **前端界面无需修改**：现有Vue组件可直接使用
- **向后兼容**：原有API接口继续可用

### 2. 数据完整性
- **字段映射准确**：严格按照提供的对应关系
- **类型转换安全**：包含错误处理和默认值
- **扩展性良好**：保留原始字段用于未来扩展

### 3. 易于维护
- **模块化设计**：数据适配器独立封装
- **调试友好**：提供数据转换验证接口
- **日志完整**：详细的错误日志和转换日志

## 部署建议

### 1. 生产环境配置

```python
# 在生产环境中，建议添加以下配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["your-frontend-domain.com"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

### 2. 性能优化

- 对于大批量数据，建议分批处理
- 添加缓存机制减少重复计算
- 使用异步处理提高并发性能

### 3. 监控告警

- 监控API响应时间
- 监控数据转换成功率
- 设置预测准确性告警

## 常见问题

### Q1: 如何处理缺失字段？
A: 数据适配器包含完整的错误处理，缺失字段会使用合理的默认值。

### Q2: 时间戳格式不一致怎么办？
A: `generate_week_from_timestamp()`方法支持毫秒时间戳，并包含异常处理。

### Q3: 如何验证数据转换正确性？
A: 使用`/api/convert/real-to-system`接口可以验证转换逻辑。

### Q4: 前端如何切换到新的数据源？
A: 使用`frontend_api_adapter.js`中的适配器类，替换原有的API调用即可。

## 联系支持

如有问题，请检查：
1. 服务是否正常启动（访问`/api/health`）
2. 数据格式是否符合`RealApiDataPoint`模型
3. 查看服务日志获取详细错误信息
