package com.telecom.nrm.service;

import com.alibaba.fastjson.JSONObject;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 分光器端口预警业务服务接口
 * 对应前端 splitter-port.vue 页面的所有业务逻辑
 * 
 * 主要功能：
 * 1. 统计数据计算和获取
 * 2. 设备列表查询和数据处理
 * 3. 实占率和健康状态计算
 * 4. 预测分析算法调用
 * 5. 趋势数据分析
 * 6. 数据导出处理
 * 7. 预警处理业务逻辑
 */
public interface SplitterService {

    /**
     * 获取统计数据
     * 🎭 替换前端 statistics 演示数据
     * 计算正常设备、注意设备、告警设备数量和总设备数
     * 
     * @return 包含统计信息的JSON对象
     */
    JSONObject getStatistics();

    /**
     * 计算设备实占率
     * 📊 对应前端 calculateOccupancyRate 计算逻辑
     * 实占率 = (分光器容量 - 分光器空闲数) / 分光器容量 * 100
     * 
     * @param device 设备数据JSON对象
     * @return 实占率百分比
     */
    double calculateOccupancyRate(JSONObject device);

    /**
     * 计算设备健康状态
     * 📊 对应前端 calculateHealthStatus 计算逻辑
     * 根据实占率判断：>=90%为告警，>=80%为注意，<80%为正常
     * 
     * @param occupancyRate 实占率
     * @return 健康状态字符串（normal/attention/alarm）
     */
    String calculateHealthStatus(double occupancyRate);

    /**
     * 获取设备分光器详情
     * 🎭 替换前端 splitterDetails 演示数据
     * 查询指定设备下所有分光器的详细信息
     * 
     * @param deviceCode 设备编码
     * @return 分光器详情列表
     */
    List<JSONObject> getSplitterDetails(String deviceCode);

    /**
     * 单设备预测分析
     * 🎭 替换前端 predictionData 演示数据
     * 根据历史数据对单个设备进行预测分析
     * 
     * @param request 预测请求参数，包含deviceCode和period
     * @return 预测分析结果JSON对象
     */
    JSONObject predictSingleDevice(JSONObject request);

    /**
     * 获取设备预测详情
     * 🎭 替换前端 devicePredictionData 演示数据
     * 提供更详细的预测信息，包括风险评估和业务洞察
     * 
     * @param deviceCode 设备编码
     * @return 设备预测详情JSON对象
     */
    JSONObject getDevicePredictionDetail(String deviceCode);

    /**
     * 批量预测所有设备
     * 🎭 替换前端批量预测功能
     * 对系统中所有设备执行预测分析
     * 
     * @return 批量预测结果列表
     */
    List<JSONObject> predictAllDevices();

    /**
     * 获取趋势数据
     * 🎭 替换前端 trendData 演示数据
     * 查询设备的历史使用率趋势数据
     * 
     * @param params 查询参数，包含deviceCode和timeRange
     * @return 趋势数据列表
     */
    List<JSONObject> getTrendData(JSONObject params);

    /**
     * 数据导出
     * 🎭 替换前端导出功能
     * 根据筛选条件导出设备数据为Excel文件
     * 
     * @param params 导出参数
     * @param response HTTP响应对象
     */
    void exportData(JSONObject params, HttpServletResponse response);

    /**
     * 处理设备预警
     * 🎭 替换前端预警处理功能
     * 处理设备的预警信息，更新处理状态
     * 
     * @param deviceCode 设备编码
     * @param request 处理请求参数
     * @return 处理结果JSON对象
     */
    JSONObject handleDeviceWarning(String deviceCode, JSONObject request);

    /**
     * 执行预测算法
     * 🔌 后端对接：需要集成预测算法服务
     * 可以是机器学习算法、统计算法或调用外部预测服务
     * 
     * @param historicalData 历史数据列表
     * @param period 预测周期
     * @return 预测结果JSON对象
     */
    JSONObject executePredictionAlgorithm(List<JSONObject> historicalData, String period);

    /**
     * 生成建议方案
     * 📊 根据预测结果生成扩容和调整建议
     * 
     * @param predictionResult 预测结果
     * @return 建议方案JSON对象
     */
    JSONObject generateRecommendations(JSONObject predictionResult);

    /**
     * 计算风险评估
     * 📊 根据设备状态和预测结果计算风险等级
     * 
     * @param deviceCode 设备编码
     * @return 风险评估JSON对象
     */
    JSONObject calculateRiskAssessment(String deviceCode);

    /**
     * 生成业务洞察
     * 📊 基于数据分析生成业务洞察信息
     * 
     * @param deviceCode 设备编码
     * @return 业务洞察JSON对象
     */
    JSONObject generateBusinessInsights(String deviceCode);

    /**
     * 计算预测空闲数
     * 📊 根据预测实占率计算预测空闲端口数
     * 
     * @param device 设备信息
     * @param prediction 预测结果
     * @return 预测空闲数
     */
    int calculatePredictedFreeCount(JSONObject device, JSONObject prediction);

    /**
     * 计算预测状态
     * 📊 根据预测结果计算预测健康状态
     * 
     * @param prediction 预测结果
     * @return 预测状态字符串
     */
    String calculatePredictedStatus(JSONObject prediction);

    /**
     * 生成Excel文件
     * 🔌 后端对接：需要Excel生成工具库支持
     * 
     * @param data 要导出的数据列表
     * @return Excel文件字节数组
     */
    byte[] generateExcelFile(List<JSONObject> data);

    /**
     * 更新预测结果到数据库
     * 🔌 后端对接：将预测结果保存到数据库
     * 
     * @param deviceCode 设备编码
     * @param predictionResult 预测结果
     * @return 更新记录数
     */
    int updatePredictionResult(String deviceCode, JSONObject predictionResult);

    /**
     * 获取历史数据
     * 🔌 后端对接：从数据库查询设备历史使用率数据
     * 
     * @param deviceCode 设备编码
     * @param timeRange 时间范围
     * @return 历史数据列表
     */
    List<JSONObject> getHistoricalData(String deviceCode, String timeRange);

    /**
     * 统计字典数据
     * 🎭 替换前端统计相关的字典数据
     *
     * @param params 查询参数
     * @return 统计字典数据
     */
    JSONObject getStatisticsDictionary(JSONObject params);

    /**
     * 转换 obd_port_alert API 数据格式
     * 将API返回的数据转换为前端需要的格式
     *
     * @param apiData API原始数据
     * @return 转换后的数据
     */
    JSONObject convertObdPortAlertData(JSONObject apiData);
}
